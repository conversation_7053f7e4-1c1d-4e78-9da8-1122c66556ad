
float NoteLeft;
float NoteRight;
float NoteBorder;
float ScreenAspect;
float KeyboardHeight;
int ScreenWidth;
int ScreenHeight;

struct NOTE {
	float start : START;
	float end : END;
    dword colorl : COLORL;
    dword colorr : COLORR;
};

struct VS_IN
{
	float4 pos : POSITION;
	float4 col : COLOR;
	uint vertexId : SV_VertexID;
};

struct PS_IN
{
	float4 pos : SV_POSITION;
	float4 col : COLOR;
};

// Vertex shader that generates quad vertices from point data
PS_IN VS_Note(NOTE input, uint vertexId : SV_VertexID)
{
	PS_IN output = (PS_IN)0;

	// Adjust note positions
	float start = input.start * (1 - KeyboardHeight) + KeyboardHeight;
	float end = input.end * (1 - KeyboardHeight) + KeyboardHeight;

	// Convert colors
	float4 colorlConv = float4((float)(input.colorl >> 24 & 0xff) / 255.0, (float)(input.colorl >> 16 & 0xff) / 255.0, (float)(input.colorl >> 8 & 0xff) / 255.0, (float)(input.colorl & 0xff) / 255.0);
    float4 colorrConv = float4((float)(input.colorr >> 24 & 0xff) / 255.0, (float)(input.colorr >> 16 & 0xff) / 255.0, (float)(input.colorr >> 8 & 0xff) / 255.0, (float)(input.colorr & 0xff) / 255.0);

	colorlConv.w *= colorlConv.w;
	colorlConv.w *= colorlConv.w;

	float4 cl = colorlConv;
    float4 cr = colorrConv;

	float noteBorder = 0.00091;
	float noteBorderh = round(noteBorder * ScreenWidth) / ScreenWidth;
	float noteBorderv = round(noteBorder * ScreenHeight) / ScreenHeight / ScreenAspect;

	// Generate quad vertices (6 vertices per note: 2 triangles)
	// vertexId 0-5: outer border, 6-11: inner fill
	uint quadId = vertexId / 6;
	uint localVertexId = vertexId % 6;

	float2 positions[6];
	float4 colors[6];

	if (quadId == 0) {
		// Outer border (darker)
		cl.xyz *= 0.2f;
		cr.xyz *= 0.2f;
		cl.xyz -= 0.05f;
		cr.xyz -= 0.05f;
		cl.xyz = clamp(cl.xyz, 0, 1);
		cr.xyz = clamp(cr.xyz, 0, 1);

		// Triangle 1: (left,start), (left,end), (right,end)
		// Triangle 2: (left,start), (right,end), (right,start)
		positions[0] = float2(NoteLeft, start);
		positions[1] = float2(NoteLeft, end);
		positions[2] = float2(NoteRight, end);
		positions[3] = float2(NoteLeft, start);
		positions[4] = float2(NoteRight, end);
		positions[5] = float2(NoteRight, start);

		colors[0] = cl;
		colors[1] = cl;
		colors[2] = cr;
		colors[3] = cl;
		colors[4] = cr;
		colors[5] = cr;
	} else {
		// Inner fill (brighter)
		cl = colorlConv;
		cr = colorrConv;
		cl.xyz += 0.1f;
		cr.xyz -= 0.3f;
		cl.xyz = clamp(cl.xyz, 0, 1);
		cr.xyz = clamp(cr.xyz, 0, 1);

		float borderTop = end - noteBorderv;
		float borderBottom = start + noteBorderv;
		float borderLeft = NoteLeft + noteBorderh;
		float borderRight = NoteRight - noteBorderh;

		if (borderTop < borderBottom) {
			// Degenerate triangle
			output.pos = float4(0, 0, 0, 0);
			output.col = float4(0, 0, 0, 0);
			return output;
		}

		positions[0] = float2(borderLeft, borderBottom);
		positions[1] = float2(borderLeft, borderTop);
		positions[2] = float2(borderRight, borderTop);
		positions[3] = float2(borderLeft, borderBottom);
		positions[4] = float2(borderRight, borderTop);
		positions[5] = float2(borderRight, borderBottom);

		colors[0] = cl;
		colors[1] = cl;
		colors[2] = cr;
		colors[3] = cl;
		colors[4] = cr;
		colors[5] = cr;
	}

	output.pos = float4(positions[localVertexId], 0, 1);
	output.pos.xy = output.pos.xy * 2 - 1;
	output.col = colors[localVertexId];

	return output;
}

// Geometry shader removed for Wine/Linux compatibility
// All geometry generation is now handled in the vertex shader

float4 PS(PS_IN input) : SV_Target
{
	return input.col;
}