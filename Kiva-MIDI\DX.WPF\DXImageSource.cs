﻿using System;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media.Imaging;

namespace Kiva_MIDI
{
    public class DXImageSource : D3DImage, IDisposable
    {
        private bool isDisposed;
        private SharpDX.Direct3D11.Texture2D lastTexture = null;

        public DXImageSource()
        {
            // DirectX11-only implementation for Wine/Linux compatibility
        }

        ~DXImageSource() { Dispose(false); }

        public void Dispose() { Dispose(true); }

        protected void Dispose(bool disposing)
        {
            if (IsDisposed)
                return;

            if (disposing)
            {
                // DirectX11-only implementation - no D3D9 surface needed
                GC.SuppressFinalize(this);
            }
            isDisposed = true;
        }

        public bool IsDisposed { get { return isDisposed; } }

        public void Invalidate()
        {
            if (IsDisposed)
                throw new ObjectDisposedException(GetType().Name);

            // For DirectX11-only implementation
            Lock();
            AddDirtyRect(new Int32Rect(0, 0, base.PixelWidth, base.PixelHeight));
            Unlock();
        }

        public void SetBackBuffer(SharpDX.Direct3D11.Texture2D texture)
        {
            lastTexture = texture;
            // DirectX11-only implementation - simplified for Wine compatibility
            // We'll implement a different approach that doesn't require D3D9 sharing
        }

        protected override void OnPropertyChanged(DependencyPropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);
            if(lastTexture != null)
            {
                SetBackBuffer(lastTexture);
            }
        }

        // DirectX11-only implementation - no more D3D9 dependencies
        // This will be updated to work with Wine/Linux compatibility
    }
}
