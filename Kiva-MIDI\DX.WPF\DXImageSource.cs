using System;
using System.Windows;
using System.Windows.Interop;
using System.Windows.Media.Imaging;
using System.Windows.Controls;

namespace Kiva_MIDI
{
    // DirectX11-only implementation for Wine/Linux compatibility
    // Using WriteableBitmap instead of D3DImage to avoid D3D9 interop issues
    public class DXImageSource : IDisposable
    {
        private bool isDisposed;
        private SharpDX.Direct3D11.Texture2D lastTexture = null;
        private WriteableBitmap bitmap;
        private Image imageControl;

        public DXImageSource()
        {
            // DirectX11-only implementation for Wine/Linux compatibility
            // We'll create a WriteableBitmap when we know the size
        }

        ~DXImageSource() { Dispose(false); }

        public void Dispose() { Dispose(true); }

        protected void Dispose(bool disposing)
        {
            if (IsDisposed)
                return;

            if (disposing)
            {
                bitmap = null;
                GC.SuppressFinalize(this);
            }
            isDisposed = true;
        }

        public bool IsDisposed { get { return isDisposed; } }

        // Property to check if front buffer is available (for compatibility)
        public bool IsFrontBufferAvailable { get { return !isDisposed; } }

        // Event for compatibility with existing code
        public event EventHandler IsFrontBufferAvailableChanged;

        public int PixelWidth { get { return bitmap?.PixelWidth ?? 0; } }
        public int PixelHeight { get { return bitmap?.PixelHeight ?? 0; } }

        public void Invalidate()
        {
            if (IsDisposed)
                throw new ObjectDisposedException(GetType().Name);

            // For DirectX11-only implementation, we'll copy texture data to WriteableBitmap
            // This is a simplified approach that works better with Wine
            if (bitmap != null && lastTexture != null)
            {
                // TODO: Copy texture data to WriteableBitmap
                // For now, just trigger any update events
                IsFrontBufferAvailableChanged?.Invoke(this, EventArgs.Empty);
            }
        }

        public void SetBackBuffer(SharpDX.Direct3D11.Texture2D texture)
        {
            if (IsDisposed)
                return;

            lastTexture = texture;

            if (texture != null)
            {
                // Create or recreate bitmap if size changed
                if (bitmap == null || bitmap.PixelWidth != texture.Description.Width || bitmap.PixelHeight != texture.Description.Height)
                {
                    bitmap = new WriteableBitmap(
                        texture.Description.Width,
                        texture.Description.Height,
                        96, 96,
                        System.Windows.Media.PixelFormats.Bgra32,
                        null);
                }

                // For now, create a simple colored bitmap as placeholder
                // In a full implementation, this would copy the texture data via CPU readback
                bitmap.Lock();
                try
                {
                    // Fill with a dark color to show it's working
                    unsafe
                    {
                        int* pixels = (int*)bitmap.BackBuffer;
                        int pixelCount = bitmap.PixelWidth * bitmap.PixelHeight;
                        for (int i = 0; i < pixelCount; i++)
                        {
                            pixels[i] = 0xFF202020; // Dark gray BGRA
                        }
                    }
                    bitmap.AddDirtyRect(new Int32Rect(0, 0, bitmap.PixelWidth, bitmap.PixelHeight));
                }
                finally
                {
                    bitmap.Unlock();
                }
            }
        }

        // Method to get the bitmap for display
        public WriteableBitmap GetBitmap()
        {
            return bitmap;
        }
    }
}
