﻿using System;
using System.Windows;
using System.Windows.Interop;
using SharpDX.Direct3D9;

namespace Kiva_MIDI
{
    public class DXImageSource : D3DImage, IDisposable
    {
        public DXImageSource()
        {
            StartD3D9();
        }
        ~DXImageSource() { Dispose(false); }

        public void Dispose() { Dispose(true); }

        protected void Dispose(bool disposing)
        {
            if (IsDisposed)
                return;

            if (disposing)
            {
                SetBackBuffer((Texture)null);
                fallbackTexture?.Dispose();
                fallbackTexture = null;
                GC.SuppressFinalize(this);
            }
            EndD3D9();
            isDisposed = true;
        }
        bool isDisposed;

        public bool IsDisposed { get { return isDisposed; } }

        public bool IsSharedResourceWorking()
        {
            return useSharedResources;
        }

        public Texture GetFallbackTexture()
        {
            return fallbackTexture;
        }

        public void InvalidateFallback()
        {
            if (!useSharedResources && fallbackTexture != null)
            {
                Invalidate();
            }
        }

        public void Invalidate()
        {
            if (IsDisposed)
                throw new ObjectDisposedException(GetType().Name);

            if (backBuffer != null)
            {
                Lock();
                AddDirtyRect(new Int32Rect(0, 0, base.PixelWidth, base.PixelHeight));
                Unlock();
            }
        }

        SharpDX.Direct3D11.Texture2D lastTexture = null;
        Texture fallbackTexture = null;
        bool useSharedResources = true;

        public void SetBackBuffer(SharpDX.Direct3D11.Texture2D texture)
        {
            lastTexture = texture;

            if (texture == null)
            {
                SetBackBuffer((Texture)null);
                return;
            }

            try
            {
                var sharedTexture = DXSharing.GetSharedD3D9(d3d9.Device, texture);
                SetBackBuffer(sharedTexture);
                useSharedResources = true;
                System.Diagnostics.Debug.WriteLine("Successfully created shared D3D9 texture");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to create shared D3D9 texture, using fallback: {ex.Message}");
                useSharedResources = false;

                // Create fallback texture if needed
                if (fallbackTexture == null ||
                    fallbackTexture.GetLevelDescription(0).Width != texture.Description.Width ||
                    fallbackTexture.GetLevelDescription(0).Height != texture.Description.Height)
                {
                    fallbackTexture?.Dispose();
                    var format = DXSharing.ToD3D9(texture.Description.Format);
                    if (format == SharpDX.Direct3D9.Format.Unknown)
                        format = SharpDX.Direct3D9.Format.A8R8G8B8;

                    System.Diagnostics.Debug.WriteLine($"Creating fallback D3D9 texture: {texture.Description.Width}x{texture.Description.Height}, format: {format}");
                    fallbackTexture = new Texture(d3d9.Device, texture.Description.Width, texture.Description.Height, 1,
                        SharpDX.Direct3D9.Usage.RenderTarget, format, SharpDX.Direct3D9.Pool.Default);
                }

                SetBackBuffer(fallbackTexture);
                System.Diagnostics.Debug.WriteLine("Using fallback D3D9 texture for rendering");
            }
        }

        protected override void OnPropertyChanged(DependencyPropertyChangedEventArgs e)
        {
            base.OnPropertyChanged(e);
            if(lastTexture != null)
            {
                SetBackBuffer(lastTexture);
            }
        }

        Texture backBuffer;

        public void SetBackBuffer(Texture texture)
        {
            if (IsDisposed)
                throw new ObjectDisposedException(GetType().Name);

            Texture toDelete = null;
            try
            {
                if (texture != backBuffer)
                {
                    // if it's from the private (SDX9ImageSource) D3D9 device, dispose of it
                    if (backBuffer != null && backBuffer.Device.NativePointer == d3d9.Device.NativePointer)
                        toDelete = backBuffer;
                    backBuffer = texture;
                }

                if (texture != null)
                {
                    using (Surface surface = texture.GetSurfaceLevel(0))
                    {
                        Lock();
                        SetBackBuffer(D3DResourceType.IDirect3DSurface9, surface.NativePointer);
                        AddDirtyRect(new Int32Rect(0, 0, base.PixelWidth, base.PixelHeight));
                        Unlock();
                    }
                }
                else
                {
                    Lock();
                    SetBackBuffer(D3DResourceType.IDirect3DSurface9, IntPtr.Zero);
                    AddDirtyRect(new Int32Rect(0, 0, base.PixelWidth, base.PixelHeight));
                    Unlock();
                }
            }
            finally
            {
                if (toDelete != null)
                {
                    toDelete.Dispose();
                }
            }
        }

        #region (private, static / shared) D3D9: d3d9

        static int activeClients;
        static D3D9 d3d9;

        private static void StartD3D9()
        {
            if (activeClients == 0)
                d3d9 = new D3D9();
            activeClients++;
        }

        private static void EndD3D9()
        {
            activeClients--;
            if (activeClients == 0)
                d3d9.Dispose();
        }

        #endregion
    }
}
