﻿
float Height;
float Left;
float Right;
float Aspect;
dword BarColor;
int ScreenWidth;
int ScreenHeight;

struct KEY
{
	dword colorl : COLORL;
	dword colorr : COLORR;
	float left : LEFT;
	float right : RIGHT;
	float distance : DISTANCE;
	dword meta : META;
};

struct PS_IN
{
	float4 pos : SV_POSITION;
	float4 col : COLOR;
};

// Vertex shader that generates quad vertices from key data
PS_IN VS(KEY input, uint vertexId : SV_VertexID)
{
	PS_IN output = (PS_IN)0;

	// Check if this is a white or black key
	bool isBlackKey = (input.meta & 1) != 0;

	if (isBlackKey) {
		// Black key rendering
		float dist = input.distance * Height * 0.05;
		float bez = 0.05;

		float left = (input.left - Left) / (Right - Left);
		float right = (input.right - Left) / (Right - Left);
		float top = Height + Height * 0.08 - dist * 0 + bez * Height;
		float bottom = Height * 0.4 - dist + bez * Height;

		float ileft = left + bez * Height * Aspect;
		float iright = right - bez * Height * Aspect;
		float itop = top - bez * Height;
		float ibottom = bottom + bez * Height;

		float4 colorlConv = float4((float)(input.colorl >> 24 & 0xff) / 255.0, (float)(input.colorl >> 16 & 0xff) / 255.0, (float)(input.colorl >> 8 & 0xff) / 255.0, (float)(input.colorl & 0xff) / 255.0);
		float4 colorrConv = float4((float)(input.colorr >> 24 & 0xff) / 255.0, (float)(input.colorr >> 16 & 0xff) / 255.0, (float)(input.colorr >> 8 & 0xff) / 255.0, (float)(input.colorr & 0xff) / 255.0);
		float4 colorl = float4(colorlConv.xyz * colorlConv.w, 1);
		float4 colorr = float4(colorrConv.xyz * colorrConv.w, 1);

		// Generate vertices for black key (simplified to one quad)
		uint localVertexId = vertexId % 6;
		float2 positions[6];
		float4 colors[6];

		positions[0] = float2(ileft, itop);
		positions[1] = float2(iright, itop);
		positions[2] = float2(iright, ibottom);
		positions[3] = float2(ileft, itop);
		positions[4] = float2(iright, ibottom);
		positions[5] = float2(ileft, ibottom);

		colors[0] = colorl + float4(0.1, 0.1, 0.1, 0);
		colors[1] = colorl + float4(0.1, 0.1, 0.1, 0);
		colors[2] = colorr + float4(0.1, 0.1, 0.1, 0);
		colors[3] = colorl + float4(0.1, 0.1, 0.1, 0);
		colors[4] = colorr + float4(0.1, 0.1, 0.1, 0);
		colors[5] = colorr + float4(0.1, 0.1, 0.1, 0);

		output.pos = float4(positions[localVertexId], 0, 1);
		output.pos.xy = output.pos.xy * 2 - 1;
		output.col = colors[localVertexId];

	} else {
		// White key rendering
		float dist = input.distance * Height * 0.08;

		float left = (input.left - Left) / (Right - Left);
		float right = (input.right - Left) / (Right - Left);
		float top = Height + Height * 0.08 - dist;
		float bottom = 0 - dist;

		float bez = 0.07;
		float ileft = left + bez * Height * Aspect;
		float iright = right - bez * Height * Aspect;
		float itop = top - bez * Height;
		float ibottom = bottom + bez * Height;

		float4 colorlConv = float4((float)(input.colorl >> 24 & 0xff) / 255.0, (float)(input.colorl >> 16 & 0xff) / 255.0, (float)(input.colorl >> 8 & 0xff) / 255.0, (float)(input.colorl & 0xff) / 255.0);
		float4 colorrConv = float4((float)(input.colorr >> 24 & 0xff) / 255.0, (float)(input.colorr >> 16 & 0xff) / 255.0, (float)(input.colorr >> 8 & 0xff) / 255.0, (float)(input.colorr & 0xff) / 255.0);
		float4 colorl = float4(colorlConv.xyz * colorlConv.w + (1 - colorlConv.w), 1);
		float4 colorr = float4(colorrConv.xyz * colorrConv.w + (1 - colorrConv.w), 1);

		// Generate vertices for white key (simplified to one quad)
		uint localVertexId = vertexId % 6;
		float2 positions[6];
		float4 colors[6];

		positions[0] = float2(ileft, itop);
		positions[1] = float2(iright, itop);
		positions[2] = float2(iright, ibottom);
		positions[3] = float2(ileft, itop);
		positions[4] = float2(iright, ibottom);
		positions[5] = float2(ileft, ibottom);

		colors[0] = colorl - float4(0.1, 0.1, 0.1, 0);
		colors[1] = colorl - float4(0.1, 0.1, 0.1, 0);
		colors[2] = colorr - float4(0.1, 0.1, 0.1, 0);
		colors[3] = colorl - float4(0.1, 0.1, 0.1, 0);
		colors[4] = colorr - float4(0.1, 0.1, 0.1, 0);
		colors[5] = colorr - float4(0.1, 0.1, 0.1, 0);

		output.pos = float4(positions[localVertexId], 0, 1);
		output.pos.xy = output.pos.xy * 2 - 1;
		output.col = colors[localVertexId];
	}

	return output;
}

// Geometry shader functions removed for Wine/Linux compatibility
// All geometry generation is now handled in the vertex shader

// GS_White geometry shader removed for Wine/Linux compatibility
// White key rendering is now handled in the vertex shader

// GS_Black geometry shader removed for Wine/Linux compatibility
// Black key rendering is now handled in the vertex shader

float4 PS(PS_IN input) : SV_Target
{
	return input.col;
}