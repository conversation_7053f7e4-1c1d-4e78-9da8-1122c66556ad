<h1 align="center"><PERSON><PERSON></h1>

<p align="center">
    <img src="https://i.imgur.com/g0XBUMN.png" width="256" style=""/>
    <br />
    <strong>Fast, Easy, Modern</strong>
</p>

<p align="center">
    <a href="https://github.com/arduano/Kiva/releases/"><img src="https://img.shields.io/github/release/arduano/Kiva.svg?style=flat-square" alt="GitHub release"></a>
    <a href="https://github.com/arduano/Kiva/releases/"><img src="https://img.shields.io/github/downloads/arduano/Kiva/total.svg?style=flat-square" alt="GitHub release"></a>
    <a href="https://github.com/arduano/Kiva/blob/master/LICENSE"><img src="https://img.shields.io/badge/license-DBAD-blue.svg?style=flat-square" alt="DBAD license"></a>
    <a href="http://makeapullrequest.com"><img src="https://img.shields.io/badge/PRs-welcome-brightgreen.svg?style=flat-square" alt="PRs Welcome"></a>
    <a href="https://discord.gg/Aj4cb5"><img src="https://img.shields.io/discord/549344616210628609.svg?style=flat-square" alt="Discord"></a>
    <a href="https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=M9XRCSPYSMBCA&source=url"><img src="https://img.shields.io/badge/Donate-PayPal-green.svg?style=flat-square" alt="Donate"></a>
</p>

## Features
- Extremely fast multithreaded rendering, best optimized for medium to high end systems.
- Support for switching between KDMAPI and WinMM in the settings. do not kdmapi patch kiva.
- Support for Zenith color palettes and midi color events
- Epic transparent or semi transparent background if you want
- 256 key support, as well as dynamic key range option (animating between 88 and 128 keys depending on the notes on screen)
- 2 different keyboard designs, as well as option to disable keyboard

## Installation
You can download the latest version of Kiva for Windows 64-bit using the [installer](https://github.com/arduano/Kiva/releases/latest/download/KivaInstaller.exe). An internet connection is required.
Kiva has **fully automatic updates**, downloading the update in the background and installing it automatically next time the program restarts.
If you wish to opt out from auto updates, use the [portable version](https://github.com/arduano/Kiva/releases/latest/download/KivaPortable.zip).

DirectX 11 is required for the program to run, which you can find [here](https://www.microsoft.com/en-us/download/details.aspx?id=17431).

## Wine/Linux Compatibility

Kiva has been optimized for better Wine/Linux compatibility:

- **DirectX 11 Only**: Removed DirectX 9 dependencies for better Wine support
- **No Geometry Shaders**: Converted to vertex/pixel shaders for broader compatibility
- **Improved Error Handling**: Better error messages for Wine-specific issues

### Running on Wine/Linux:

1. Install Wine (version 6.0+ recommended)
2. Install DirectX 11 support: `winetricks d3d11`
3. For better performance, also install: `winetricks dxvk`
4. Run Kiva with: `wine Kiva.exe`

If you encounter graphics issues, try:
- `winetricks vcrun2019` (Visual C++ Runtime)
- `winetricks corefonts` (Core fonts)
- Setting Wine to Windows 10 mode: `winecfg`

## Usage
After downloading the app, install and run the program. Many settings have a question mark button next to them, that should help clear up the purpose of many confusing settings.

## License
Kiva is licensed under the terms of the [Don't Be a Dick Public License](https://github.com/arduano/Kiva/blob/master/LICENSE).

## Screenshots
<img src="https://i.imgur.com/YD0wHE1.png" width="512" />
<img src="https://i.imgur.com/48GfALp.png" width="512" />
<img src="https://i.imgur.com/QpgjYcv.png" width="512" />
