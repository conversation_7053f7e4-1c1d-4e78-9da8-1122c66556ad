﻿
float Height;
float Left;
float Right;
float Aspect;
dword BarColor;
int ScreenWidth;
int ScreenHeight;

struct KEY
{
	dword colorl : COLORL;
	dword colorr : COLORR;
	float left : LEFT;
	float right : RIGHT;
	float distance : DISTANCE;
	dword meta : META;
};

struct PS_IN
{
	float4 pos : SV_POSITION;
	float4 col : COLOR;
};

// Vertex shader that generates quad vertices from key data
PS_IN VS(KEY input, uint vertexId : SV_VertexID)
{
	PS_IN output = (PS_IN)0;

	// Check if this is a white or black key, or bar
	bool isBlackKey = (input.meta & 1) != 0;
	bool isPressed = (input.meta & 2) != 0;

	if (isBlackKey) {
		// Black key rendering
		float height = Height * 0.94;
		float bez = 0.015;

		float left = (input.left - Left) / (Right - Left);
		float right = (input.right - Left) / (Right - Left);
		float top = height;
		float bottom = height * 0.35 + bez * height;

		float ileft = left + bez * height * Aspect;
		float iright = right - bez * height * Aspect;
		float itop = top + bez * height * 2.5;
		if (isPressed) itop = top;
		float ibottom = bottom + bez * height;
		if (!isPressed) ibottom = bottom + bez * height * 2.5;

		float4 colorlConv = float4((float)(input.colorl >> 24 & 0xff) / 255.0, (float)(input.colorl >> 16 & 0xff) / 255.0, (float)(input.colorl >> 8 & 0xff) / 255.0, (float)(input.colorl & 0xff) / 255.0);
		float4 colorrConv = float4((float)(input.colorr >> 24 & 0xff) / 255.0, (float)(input.colorr >> 16 & 0xff) / 255.0, (float)(input.colorr >> 8 & 0xff) / 255.0, (float)(input.colorr & 0xff) / 255.0);
		float4 colorl = float4(colorlConv.xyz * colorlConv.w, 1);
		float4 colorr = float4(colorrConv.xyz * colorrConv.w, 1);

		// Generate vertices for black key (simplified to one quad)
		uint localVertexId = vertexId % 6;
		float2 positions[6];
		float4 colors[6];

		positions[0] = float2(ileft, itop);
		positions[1] = float2(iright, itop);
		positions[2] = float2(iright, ibottom);
		positions[3] = float2(ileft, itop);
		positions[4] = float2(iright, ibottom);
		positions[5] = float2(ileft, ibottom);

		float dimValue = isPressed ? -0.3 : 0.0;
		colors[0] = colorl + float4(dimValue, dimValue, dimValue, 0);
		colors[1] = colorl + float4(dimValue, dimValue, dimValue, 0);
		colors[2] = colorr + float4(dimValue, dimValue, dimValue, 0);
		colors[3] = colorl + float4(dimValue, dimValue, dimValue, 0);
		colors[4] = colorr + float4(dimValue, dimValue, dimValue, 0);
		colors[5] = colorr + float4(dimValue, dimValue, dimValue, 0);

		output.pos = float4(positions[localVertexId], 0, 1);
		output.pos.xy = output.pos.xy * 2 - 1;
		output.col = colors[localVertexId];

	} else {
		// White key rendering
		float height = Height * 0.94;

		float left = (input.left - Left) / (Right - Left);
		float right = (input.right - Left) / (Right - Left);
		float top = height;
		float bottom = 0;

		float bez = 0.04;
		float itop = top - bez * height;
		float ibottom = bottom + bez * height * 1.4;
		if (isPressed) ibottom = bottom + bez * height / 3;

		float4 colorlConv = float4((float)(input.colorl >> 24 & 0xff) / 255.0, (float)(input.colorl >> 16 & 0xff) / 255.0, (float)(input.colorl >> 8 & 0xff) / 255.0, (float)(input.colorl & 0xff) / 255.0);
		float4 colorrConv = float4((float)(input.colorr >> 24 & 0xff) / 255.0, (float)(input.colorr >> 16 & 0xff) / 255.0, (float)(input.colorr >> 8 & 0xff) / 255.0, (float)(input.colorr & 0xff) / 255.0);
		float4 colorl = float4(colorlConv.xyz * colorlConv.w + (1 - colorlConv.w), 1);
		float4 colorr = float4(colorrConv.xyz * colorrConv.w + (1 - colorrConv.w), 1);

		// Generate vertices for white key (simplified to one quad)
		uint localVertexId = vertexId % 6;
		float2 positions[6];
		float4 colors[6];

		positions[0] = float2(left, top);
		positions[1] = float2(right, top);
		positions[2] = float2(right, ibottom);
		positions[3] = float2(left, top);
		positions[4] = float2(right, ibottom);
		positions[5] = float2(left, ibottom);

		float4 coll2 = colorl;
		coll2.xyz *= 0.8;
		colors[0] = coll2;
		colors[1] = coll2;
		colors[2] = colorr;
		colors[3] = coll2;
		colors[4] = colorr;
		colors[5] = colorr;

		output.pos = float4(positions[localVertexId], 0, 1);
		output.pos.xy = output.pos.xy * 2 - 1;
		output.col = colors[localVertexId];
	}

	return output;
}

// Geometry shader functions removed for Wine/Linux compatibility
// All geometry generation is now handled in the vertex shader

// Bar vertex shader for the keyboard separator
PS_IN VS_Bar(KEY input, uint vertexId : SV_VertexID)
{
	PS_IN output = (PS_IN)0;

	float4 color = float4((float)(BarColor >> 24 & 0xff) / 255.0, (float)(BarColor >> 16 & 0xff) / 255.0, (float)(BarColor >> 8 & 0xff) / 255.0, (float)(BarColor & 0xff) / 255.0);

	uint localVertexId = vertexId % 6;
	float2 positions[6];
	float4 colors[6];

	positions[0] = float2(0, Height);
	positions[1] = float2(1, Height);
	positions[2] = float2(1, Height * 0.94);
	positions[3] = float2(0, Height);
	positions[4] = float2(1, Height * 0.94);
	positions[5] = float2(0, Height * 0.94);

	colors[0] = color;
	colors[1] = color;
	colors[2] = color * 0.8;
	colors[3] = color;
	colors[4] = color * 0.8;
	colors[5] = color * 0.8;

	output.pos = float4(positions[localVertexId], 0, 1);
	output.pos.xy = output.pos.xy * 2 - 1;
	output.col = colors[localVertexId];

	return output;
}

// GS_Bar geometry shader removed for Wine/Linux compatibility
// Bar rendering is now handled in the VS_Bar vertex shader

// GS_White geometry shader removed for Wine/Linux compatibility
// White key rendering is now handled in the main VS vertex shader

// GS_Black geometry shader removed for Wine/Linux compatibility
// Black key rendering is now handled in the main VS vertex shader

float4 PS(PS_IN input) : SV_Target
{
	return input.col;
}