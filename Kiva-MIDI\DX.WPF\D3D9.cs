﻿using System;
using System.Runtime.InteropServices;
using SharpDX.Direct3D9;

namespace Kiva_MIDI
{
	public class D3D9 : D3D
	{
		protected D3D9(bool b) { /* do nothing constructor */ }

		public D3D9()
			: this(null)
		{
		}

		public D3D9(DeviceEx device)
		{
			if (device != null)
			{
				//context = ???
				throw new NotSupportedException("dunno how to get the context");

				//this.device = device;
				//device.AddReference();
			}
			else
			{
				try
				{
					context = new Direct3DEx();

					PresentParameters presentparams = new PresentParameters();
					presentparams.Windowed = true;
					presentparams.SwapEffect = SwapEffect.Discard;
					presentparams.DeviceWindowHandle = GetDesktopWindow();
					presentparams.PresentationInterval = PresentInterval.Default;

					// Try hardware vertex processing first
					try
					{
						this.device = new DeviceEx(context, 0, DeviceType.Hardware, IntPtr.Zero,
							CreateFlags.HardwareVertexProcessing | CreateFlags.Multithreaded | CreateFlags.FpuPreserve, presentparams);
					}
					catch (SharpDX.SharpDXException)
					{
						// Fallback to software vertex processing for Wine compatibility
						System.Diagnostics.Debug.WriteLine("Hardware vertex processing failed, falling back to software");
						this.device = new DeviceEx(context, 0, DeviceType.Hardware, IntPtr.Zero,
							CreateFlags.SoftwareVertexProcessing | CreateFlags.Multithreaded | CreateFlags.FpuPreserve, presentparams);
					}
				}
				catch (SharpDX.SharpDXException ex)
				{
					System.Diagnostics.Debug.WriteLine($"DirectX9 device creation failed: {ex.Message}");
					throw new NotSupportedException("Failed to create DirectX9 device. This may be due to Wine compatibility issues.", ex);
				}
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing)
			{
				Set(ref device, null);
				Set(ref context, null);
			}
		}

		public bool IsDisposed { get { return device == null; } }

		[DllImport("user32.dll", SetLastError = false)]
		static extern IntPtr GetDesktopWindow();

		protected Direct3DEx context;
		protected DeviceEx device;

		public DeviceEx Device { get { return device.GetOrThrow(); } }

		Texture renderTarget;

		public override void Reset(int w, int h)
		{
			device.GetOrThrow();

			if (w < 1)
				throw new ArgumentOutOfRangeException("w");
			if (h < 1)
				throw new ArgumentOutOfRangeException("h");

			try
			{
				Set(ref renderTarget, new Texture(this.device, w, h, 1, Usage.RenderTarget, Format.A8R8G8B8, Pool.Default));

				// Set render target
				using (var surface = renderTarget.GetSurfaceLevel(0))
					device.SetRenderTarget(0, surface);
			}
			catch (SharpDX.SharpDXException ex)
			{
				System.Diagnostics.Debug.WriteLine($"DirectX9 Reset failed: {ex.Message}");
				// Try with different parameters for Wine compatibility
				try
				{
					Set(ref renderTarget, new Texture(this.device, w, h, 1, Usage.RenderTarget, Format.X8R8G8B8, Pool.Default));
					using (var surface = renderTarget.GetSurfaceLevel(0))
						device.SetRenderTarget(0, surface);
				}
				catch (SharpDX.SharpDXException ex2)
				{
					System.Diagnostics.Debug.WriteLine($"DirectX9 Reset fallback also failed: {ex2.Message}");
					throw new InvalidOperationException("Failed to create DirectX9 render target. Wine compatibility issue.", ex2);
				}
			}
		}

		protected T Prepared<T>(ref T property)
		{
			device.GetOrThrow();
			if (property == null)
				Reset(1, 1);
			return property;
		}

		public Texture RenderTarget { get { return Prepared(ref renderTarget); } }

		public override void SetBackBuffer(DXImageSource dximage) { dximage.SetBackBuffer(RenderTarget); }

		public override System.Windows.Media.Imaging.WriteableBitmap ToImage() { throw new NotImplementedException(); }
	}
}
