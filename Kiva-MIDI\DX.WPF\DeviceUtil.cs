﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SharpDX.DXGI;
using Direct3D11 = SharpDX.Direct3D11;
using Direct3D = SharpDX.Direct3D;

namespace Kiva_MIDI
{
	public static class DeviceUtil
	{
		public static SharpDX.Direct3D11.Device Create11(
			Direct3D11.DeviceCreationFlags cFlags = Direct3D11.DeviceCreationFlags.None,
			Direct3D.FeatureLevel minLevel = Direct3D.FeatureLevel.Level_9_1
		)
		{
			using (var dg = new DisposeGroup())
			{
				// Try multiple approaches for Wine/Linux compatibility
				try
				{
					// First try: Hardware with debug layer disabled for Wine compatibility
					var flags = cFlags & ~Direct3D11.DeviceCreationFlags.Debug;
					var level = Direct3D11.Device.GetSupportedFeatureLevel();
					if (level < minLevel)
						return null;

					return new Direct3D11.Device(Direct3D.DriverType.Hardware, flags, level);
				}
				catch
				{
					try
					{
						// Second try: WARP software renderer for Wine compatibility
						return new Direct3D11.Device(Direct3D.DriverType.Warp, cFlags & ~Direct3D11.DeviceCreationFlags.Debug, Direct3D.FeatureLevel.Level_11_0);
					}
					catch
					{
						try
						{
							// Third try: Reference driver as last resort
							return new Direct3D11.Device(Direct3D.DriverType.Reference, cFlags & ~Direct3D11.DeviceCreationFlags.Debug, Direct3D.FeatureLevel.Level_11_0);
						}
						catch
						{
							// All attempts failed
							return null;
						}
					}
				}
			}
		}
	}
}
