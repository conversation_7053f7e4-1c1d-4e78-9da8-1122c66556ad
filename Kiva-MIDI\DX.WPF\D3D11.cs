﻿using System;
using System.Collections.Generic;
using SharpDX.Direct3D;
using SharpDX.Direct3D11;
using SharpDX.DXGI;
using Device = SharpDX.Direct3D11.Device;
using SharpDX.Mathematics.Interop;

namespace Kiva_MIDI
{
    public class D3D11 : D3D
    {
        protected Device device;

        protected D3D11(bool b) { /* do nothing constructor */ }

        public D3D11(FeatureLevel minLevel)
        {
            device = DeviceUtil.Create11(DeviceCreationFlags.BgraSupport, minLevel);
            if (device == null)
                throw new NotSupportedException();
        }

        public D3D11()
        {
            Device dev = null;
            try
            {
                device = DeviceUtil.Create11(DeviceCreationFlags.BgraSupport);
                if (device == null)
                    throw new NotSupportedException("DirectX11 device creation returned null");
            }
            catch (SharpDX.SharpDXException ex)
            {
                System.Diagnostics.Debug.WriteLine($"DirectX11 device creation failed: {ex.Message}");
                throw new NotSupportedException($"DirectX11 device creation failed. This may be due to Wine compatibility issues. Error: {ex.Message}", ex);
            }
        }

        public D3D11(Adapter a)
        {
            if (a == null)
            {
                device = DeviceUtil.Create11(DeviceCreationFlags.BgraSupport, FeatureLevel.Level_11_0);
                if (device == null)
                    throw new NotSupportedException();
            }
            device = new Device(a);
        }

        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            // NOTE: SharpDX 1.3 requires explicit Dispose() of everything
            Set(ref device, null);
            Set(ref renderTarget, null);
            Set(ref renderTargetView, null);
            //Set(ref depthStencil, null);
            //Set(ref depthStencilView, null);
        }

        public Device Device { get { return device.GetOrThrow(); } }

        public bool IsDisposed { get { return device == null; } }

        public override void SetBackBuffer(DXImageSource dximage) { dximage.SetBackBuffer(RenderTarget); }

        protected Texture2D renderTarget;
        protected RenderTargetView renderTargetView;
        protected Texture2D renderTargetCopy;
        //protected Texture2D depthStencil;
        //protected DepthStencilView depthStencilView;

        #region RenderTargetOptionFlags

        public ResourceOptionFlags RenderTargetOptionFlags
        {
            get { return mRenderTargetOptionFlags; }
            set
            {
                if (value == mRenderTargetOptionFlags)
                    return;
                mRenderTargetOptionFlags = value;
            }
        }
        // must be shared to be displayed in a D3DImage (unless in Wine compatibility mode)
        ResourceOptionFlags mRenderTargetOptionFlags = ResourceOptionFlags.Shared;

        #endregion

        public override void Reset(int w, int h)
        {
            device.GetOrThrow();

            if (w < 1)
                throw new ArgumentOutOfRangeException("w");
            if (h < 1)
                throw new ArgumentOutOfRangeException("h");

            var desc = new Texture2DDescription
            {
                BindFlags = BindFlags.RenderTarget | BindFlags.ShaderResource,
                Format = Format.B8G8R8A8_UNorm,
                Width = w,
                Height = h,
                MipLevels = 1,
                SampleDescription = new SampleDescription(1, 0),
                Usage = ResourceUsage.Default,
                OptionFlags = RenderTargetOptionFlags,
                CpuAccessFlags = CpuAccessFlags.None,
                ArraySize = 1
            };
            try
            {
                Set(ref renderTarget, new Texture2D(this.device, desc));
                Set(ref renderTargetView, new RenderTargetView(this.device, this.renderTarget));
                Set(ref renderTargetCopy, new Texture2D(this.device, desc));
            }
            catch (SharpDX.SharpDXException ex)
            {
                System.Diagnostics.Debug.WriteLine($"DirectX11 Reset failed with shared resources: {ex.Message}");

                // Try without shared resources for Wine compatibility
                desc.OptionFlags = ResourceOptionFlags.None;
                try
                {
                    Set(ref renderTarget, new Texture2D(this.device, desc));
                    Set(ref renderTargetView, new RenderTargetView(this.device, this.renderTarget));
                    Set(ref renderTargetCopy, new Texture2D(this.device, desc));

                    // Update the flag so DXImageSource knows shared resources aren't working
                    mRenderTargetOptionFlags = ResourceOptionFlags.None;
                    System.Diagnostics.Debug.WriteLine("DirectX11 Reset succeeded without shared resources (Wine compatibility mode)");
                }
                catch (SharpDX.SharpDXException ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"DirectX11 Reset failed even without shared resources: {ex2.Message}");
                    throw new InvalidOperationException("Failed to create DirectX11 render target. Wine compatibility issue.", ex2);
                }
            }

            //Set(ref depthStencil, DXUtils.CreateTexture2D(this.device, w, h, BindFlags.DepthStencil, Format.D24_UNorm_S8_UInt));
            //Set(ref depthStencilView, new DepthStencilView(this.device, depthStencil));
            //device.ImmediateContext.OutputMerger.SetRenderTargets(depthStencilView, renderTargetView);
        }

        public override void BeginRender(DrawEventArgs args)
        {
            device.ImmediateContext.Rasterizer.SetViewports(new RawViewportF[] { new RawViewportF() {
                X = 0,
                Y = 0,
                Width = (int)args.RenderSize.Width,
                Height = (int)args.RenderSize.Height,
                MinDepth = 0.0f,
                MaxDepth = 1.0f
            } });

            device.ImmediateContext.OutputMerger.SetRenderTargets(renderTargetView);

            device.GetOrThrow();
            //Device.ImmediateContext.ClearDepthStencilView(this.DepthStencilView, DepthStencilClearFlags.Depth, 1.0f, 0);
        }

        public override void EndRender(DrawEventArgs args)
        {
            Device.ImmediateContext.Flush();
            Device.ImmediateContext.CopyResource(renderTarget, renderTargetCopy);

            // If we're using fallback mode (Wine compatibility), we need to copy texture data manually
            if (args.Target != null && !args.Target.IsSharedResourceWorking())
            {
                System.Diagnostics.Debug.WriteLine("Copying texture to fallback for Wine compatibility");
                CopyTextureToFallback(args.Target);
                args.Target.InvalidateFallback();
            }
        }

        private void CopyTextureToFallback(DXImageSource target)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting texture copy to fallback");

                // Copy the render target to a staging texture for CPU access
                var stagingDesc = renderTarget.Description;
                stagingDesc.Usage = ResourceUsage.Staging;
                stagingDesc.BindFlags = BindFlags.None;
                stagingDesc.CpuAccessFlags = CpuAccessFlags.Read;
                stagingDesc.OptionFlags = ResourceOptionFlags.None;

                using (var stagingTexture = new Texture2D(device, stagingDesc))
                {
                    // Copy from render target to staging texture
                    device.ImmediateContext.CopyResource(renderTarget, stagingTexture);

                    // Map the staging texture to get CPU access
                    var dataBox = device.ImmediateContext.MapSubresource(stagingTexture, 0, MapMode.Read, SharpDX.Direct3D11.MapFlags.None);

                    try
                    {
                        // Get the fallback D3D9 texture from the target
                        var d3d9Texture = target.GetFallbackTexture();
                        if (d3d9Texture != null)
                        {
                            // Lock the D3D9 texture for writing
                            var lockedRect = d3d9Texture.LockRectangle(0, SharpDX.Direct3D9.LockFlags.None);

                            try
                            {
                                // Copy pixel data from D3D11 to D3D9
                                unsafe
                                {
                                    byte* src = (byte*)dataBox.DataPointer;
                                    byte* dst = (byte*)lockedRect.DataPointer;

                                    int height = renderTarget.Description.Height;
                                    int width = renderTarget.Description.Width;
                                    int bytesPerPixel = 4; // BGRA format

                                    for (int y = 0; y < height; y++)
                                    {
                                        byte* srcRow = src + y * dataBox.RowPitch;
                                        byte* dstRow = dst + y * lockedRect.Pitch;

                                        for (int x = 0; x < width * bytesPerPixel; x++)
                                        {
                                            dstRow[x] = srcRow[x];
                                        }
                                    }
                                }
                            }
                            finally
                            {
                                d3d9Texture.UnlockRectangle(0);
                            }

                            System.Diagnostics.Debug.WriteLine("Texture copy to fallback completed successfully");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("No fallback texture available for copying");
                        }
                    }
                    finally
                    {
                        device.ImmediateContext.UnmapSubresource(stagingTexture, 0);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Failed to copy texture to fallback: {ex.Message}");
            }
        }

        protected T Prepared<T>(ref T property)
        {
            device.GetOrThrow();
            if (property == null)
                Reset(1, 1);
            return property;
        }

        public Texture2D RenderTarget { get { return Prepared(ref renderTargetCopy); } }
        public RenderTargetView RenderTargetView { get { return Prepared(ref renderTargetView); } }

        //public Texture2D DepthStencil { get { return Prepared(ref depthStencil); } }
        //public DepthStencilView DepthStencilView { get { return Prepared(ref depthStencilView); } }

        public override System.Windows.Media.Imaging.WriteableBitmap ToImage()
        {
            try
            {
                if (renderTarget == null)
                    return null;

                // Create staging texture for CPU access
                var stagingDesc = renderTarget.Description;
                stagingDesc.Usage = ResourceUsage.Staging;
                stagingDesc.BindFlags = BindFlags.None;
                stagingDesc.CpuAccessFlags = CpuAccessFlags.Read;
                stagingDesc.OptionFlags = ResourceOptionFlags.None;

                using (var stagingTexture = new Texture2D(device, stagingDesc))
                {
                    // Copy render target to staging texture
                    device.ImmediateContext.CopyResource(renderTarget, stagingTexture);

                    // Map the staging texture
                    var dataBox = device.ImmediateContext.MapSubresource(stagingTexture, 0, MapMode.Read, SharpDX.Direct3D11.MapFlags.None);

                    try
                    {
                        // Create WriteableBitmap
                        var bitmap = new System.Windows.Media.Imaging.WriteableBitmap(
                            renderTarget.Description.Width,
                            renderTarget.Description.Height,
                            96, 96,
                            System.Windows.Media.PixelFormats.Bgra32,
                            null);

                        // Copy pixel data
                        bitmap.Lock();
                        try
                        {
                            unsafe
                            {
                                byte* src = (byte*)dataBox.DataPointer;
                                byte* dst = (byte*)bitmap.BackBuffer;

                                int height = renderTarget.Description.Height;
                                int width = renderTarget.Description.Width;
                                int bytesPerPixel = 4;

                                for (int y = 0; y < height; y++)
                                {
                                    byte* srcRow = src + y * dataBox.RowPitch;
                                    byte* dstRow = dst + y * bitmap.BackBufferStride;

                                    for (int x = 0; x < width * bytesPerPixel; x++)
                                    {
                                        dstRow[x] = srcRow[x];
                                    }
                                }
                            }

                            bitmap.AddDirtyRect(new System.Windows.Int32Rect(0, 0, bitmap.PixelWidth, bitmap.PixelHeight));
                        }
                        finally
                        {
                            bitmap.Unlock();
                        }

                        return bitmap;
                    }
                    finally
                    {
                        device.ImmediateContext.UnmapSubresource(stagingTexture, 0);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ToImage failed: {ex.Message}");
                return null;
            }
        }
    }
}
