using System;
using System.Windows.Media.Imaging;

namespace Kiva_MIDI
{
    /// <summary>
    /// CPU-based DirectX renderer for Wine compatibility
    /// Uses pure CPU rendering instead of DirectX
    /// </summary>
    public class D3DCPU : D3D
    {
        private CPUMIDIRenderer cpuRenderer;
        private Settings settings;
        private WriteableBitmap currentBitmap;

        public D3DCPU(Settings settings)
        {
            this.settings = settings;
            this.cpuRenderer = new CPUMIDIRenderer(settings);
            System.Diagnostics.Debug.WriteLine("D3DCPU: Created CPU-based renderer");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                cpuRenderer?.Dispose();
                cpuRenderer = null;
                currentBitmap = null;
            }
        }

        public bool IsDisposed { get { return cpuRenderer == null; } }

        public override void Reset(int w, int h)
        {
            if (cpuRenderer == null)
                return;

            if (w < 1)
                throw new ArgumentOutOfRangeException("w");
            if (h < 1)
                throw new ArgumentOutOfRangeException("h");

            try
            {
                cpuRenderer.Reset(w, h);
                System.Diagnostics.Debug.WriteLine($"D3DCPU: Reset to {w}x{h}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"D3DCPU Reset failed: {ex.Message}");
                throw new InvalidOperationException("Failed to reset CPU renderer", ex);
            }
        }

        public override void BeginRender(DrawEventArgs args)
        {
            // Nothing special needed for CPU rendering
        }

        public override void RenderScene(DrawEventArgs args)
        {
            try
            {
                if (cpuRenderer != null)
                {
                    currentBitmap = cpuRenderer.Render();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"D3DCPU RenderScene failed: {ex.Message}");
            }
            
            // Call base implementation to trigger Rendering event
            base.RenderScene(args);
        }

        public override void EndRender(DrawEventArgs args)
        {
            // Nothing special needed for CPU rendering
        }

        public override void SetBackBuffer(DXImageSource dximage)
        {
            // CPU renderer doesn't use DXImageSource
            // This method is not used in fallback mode
        }

        public override WriteableBitmap ToImage()
        {
            return currentBitmap;
        }

        // Properties to access CPU renderer data
        public long LastRenderedNoteCount
        {
            get { return cpuRenderer?.LastRenderedNoteCount ?? 0; }
        }

        public long LastPolyphony
        {
            get { return cpuRenderer?.LastPolyphony ?? 0; }
        }

        public long LastNPS
        {
            get { return cpuRenderer?.LastNPS ?? 0; }
        }

        public long NotesPassedSum
        {
            get { return cpuRenderer?.NotesPassedSum ?? 0; }
        }

        public PlayingState Time
        {
            get { return cpuRenderer?.Time; }
            set { if (cpuRenderer != null) cpuRenderer.Time = value; }
        }

        public MIDIFile File
        {
            get { return cpuRenderer?.File; }
            set { if (cpuRenderer != null) cpuRenderer.File = value; }
        }
    }
}
