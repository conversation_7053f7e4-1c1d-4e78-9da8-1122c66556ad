using System;

namespace Kiva_MIDI
{
    /// <summary>
    /// CPU-based scene for Wine compatibility
    /// Uses CPU rendering instead of DirectX
    /// </summary>
    public class CPUScene : IDirect3D
    {
        public FPS FPS { get; set; }
        
        private D3DCPU renderer;
        public Settings Settings { get; set; }

        public MIDIFile File
        {
            get => renderer?.File;
            set { if (renderer != null) renderer.File = value; }
        }

        public PlayingState Time
        {
            get => renderer?.Time;
            set { if (renderer != null) renderer.Time = value; }
        }

        public long LastRenderedNoteCount
        {
            get => renderer?.LastRenderedNoteCount ?? 0;
        }

        public long LastPolyphony
        {
            get => renderer?.LastPolyphony ?? 0;
        }

        public long LastNPS
        {
            get => renderer?.LastNPS ?? 0;
        }

        public long NotesPassedSum
        {
            get => renderer?.NotesPassedSum ?? 0;
        }

        public D3DCPU Renderer
        {
            get { return renderer; }
            set
            {
                if (renderer != null)
                {
                    renderer.Rendering -= ContextRendering;
                }
                renderer = value;
                if (renderer != null)
                {
                    renderer.Rendering += ContextRendering;
                }
            }
        }

        public CPUScene(Settings settings)
        {
            Settings = settings;
            renderer = new D3DCPU(settings);
            renderer.Rendering += ContextRendering;
            System.Diagnostics.Debug.WriteLine("CPUScene: Created CPU-based scene");
        }

        void ContextRendering(object aCtx, DrawEventArgs args) 
        { 
            FPS?.AddFrame(args.TotalTime);
        }

        void IDirect3D.Reset(DrawEventArgs args)
        {
            if (renderer != null)
                renderer.Reset(args);
        }

        void IDirect3D.Render(DrawEventArgs args)
        {
            if (renderer != null)
                renderer.Render(args);
        }

        System.Windows.Media.Imaging.WriteableBitmap IDirect3D.ToImage()
        {
            if (renderer != null)
                return renderer.ToImage();
            return null;
        }
    }
}
