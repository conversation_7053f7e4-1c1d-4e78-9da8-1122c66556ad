using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace Kiva_MIDI
{
    /// <summary>
    /// CPU-based MIDI renderer for Wine compatibility
    /// Renders directly to WriteableBitmap without DirectX
    /// </summary>
    public class CPUMIDIRenderer : IDisposable
    {
        private Settings settings;
        private WriteableBitmap bitmap;
        private int width, height;
        private PlayingState time;
        private MIDIFile file;
        private object fileLock = new object();

        // Piano key layout constants
        private static readonly bool[] blackKeys = { false, true, false, true, false, false, true, false, true, false, true, false };
        private static readonly double[] whiteKeyPositions = { 0, 2, 4, 5, 7, 9, 11 };
        private static readonly double[] blackKeyPositions = { 1, 3, 6, 8, 10 };

        public long LastRenderedNoteCount { get; private set; }
        public long LastPolyphony { get; private set; }
        public long LastNPS { get; private set; }
        public long NotesPassedSum { get; private set; }

        public PlayingState Time
        {
            get => time;
            set => time = value;
        }

        public MIDIFile File
        {
            get => file;
            set => file = value;
        }

        public CPUMIDIRenderer(Settings settings)
        {
            this.settings = settings;
        }

        public void Reset(int w, int h)
        {
            if (w < 1 || h < 1) return;

            width = w;
            height = h;
            
            bitmap = new WriteableBitmap(w, h, 96, 96, PixelFormats.Bgra32, null);
            System.Diagnostics.Debug.WriteLine($"CPUMIDIRenderer: Created {w}x{h} bitmap");
        }

        public WriteableBitmap Render()
        {
            if (bitmap == null || time == null)
            {
                System.Diagnostics.Debug.WriteLine("CPUMIDIRenderer: Cannot render - bitmap or time is null");
                return bitmap;
            }

            try
            {
                bitmap.Lock();

                // Clear background
                ClearBackground();

                // Render piano keyboard
                RenderKeyboard();

                // Render notes
                RenderNotes();

                bitmap.AddDirtyRect(new Int32Rect(0, 0, width, height));

                // Debug output every 60 frames (roughly once per second at 60fps)
                static int frameCount = 0;
                frameCount++;
                if (frameCount % 60 == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"CPUMIDIRenderer: Rendered frame {frameCount}, Notes: {LastRenderedNoteCount}, Polyphony: {LastPolyphony}");
                }
            }
            finally
            {
                bitmap.Unlock();
            }

            return bitmap;
        }

        private unsafe void ClearBackground()
        {
            var bgColor = settings.General.BackgroundColor;
            uint bgColorValue = (uint)((bgColor.A << 24) | (bgColor.R << 16) | (bgColor.G << 8) | bgColor.B);
            
            uint* pixels = (uint*)bitmap.BackBuffer;
            int pixelCount = width * height;
            
            for (int i = 0; i < pixelCount; i++)
            {
                pixels[i] = bgColorValue;
            }
        }

        private unsafe void RenderKeyboard()
        {
            if (settings.General.KeyboardStyle == KeyboardStyle.None)
                return;

            int keyboardHeight = height / 4; // Bottom quarter of screen
            int keyboardTop = height - keyboardHeight;

            // Calculate key dimensions
            int whiteKeyCount = GetWhiteKeyCount();
            double whiteKeyWidth = (double)width / whiteKeyCount;
            double blackKeyWidth = whiteKeyWidth * 0.6;
            double blackKeyHeight = keyboardHeight * 0.6;

            uint* pixels = (uint*)bitmap.BackBuffer;

            // Draw white keys first
            DrawWhiteKeys(pixels, keyboardTop, keyboardHeight, whiteKeyWidth);

            // Draw black keys on top
            DrawBlackKeys(pixels, keyboardTop, blackKeyHeight, whiteKeyWidth, blackKeyWidth);

            // Debug output (only once)
            static bool keyboardDebugShown = false;
            if (!keyboardDebugShown)
            {
                System.Diagnostics.Debug.WriteLine($"CPUMIDIRenderer: Keyboard rendered - {whiteKeyCount} white keys, height: {keyboardHeight}px");
                keyboardDebugShown = true;
            }
        }

        private unsafe void DrawWhiteKeys(uint* pixels, int keyboardTop, int keyboardHeight, double whiteKeyWidth)
        {
            uint whiteColor = 0xFFFFFFFF; // White
            uint borderColor = 0xFF000000; // Black border
            
            int whiteKeyIndex = 0;
            for (int note = GetFirstNote(); note < GetLastNote(); note++)
            {
                if (!IsBlackKey(note))
                {
                    int keyLeft = (int)(whiteKeyIndex * whiteKeyWidth);
                    int keyRight = (int)((whiteKeyIndex + 1) * whiteKeyWidth);
                    
                    // Fill white key
                    FillRectangle(pixels, keyLeft, keyboardTop, keyRight - keyLeft, keyboardHeight, whiteColor);
                    
                    // Draw border
                    DrawRectangleBorder(pixels, keyLeft, keyboardTop, keyRight - keyLeft, keyboardHeight, borderColor);
                    
                    whiteKeyIndex++;
                }
            }
        }

        private unsafe void DrawBlackKeys(uint* pixels, int keyboardTop, double blackKeyHeight, double whiteKeyWidth, double blackKeyWidth)
        {
            uint blackColor = 0xFF000000; // Black
            
            int whiteKeyIndex = 0;
            for (int note = GetFirstNote(); note < GetLastNote(); note++)
            {
                if (IsBlackKey(note))
                {
                    // Position black key between white keys
                    double blackKeyCenter = (whiteKeyIndex + 0.5) * whiteKeyWidth;
                    int keyLeft = (int)(blackKeyCenter - blackKeyWidth / 2);
                    int keyWidth = (int)blackKeyWidth;
                    
                    FillRectangle(pixels, keyLeft, keyboardTop, keyWidth, (int)blackKeyHeight, blackColor);
                }
                else
                {
                    whiteKeyIndex++;
                }
            }
        }

        private unsafe void RenderNotes()
        {
            lock (fileLock)
            {
                if (file == null || !(file is MIDIMemoryFile))
                    return;

                var midiFile = file as MIDIMemoryFile;
                double currentTime = time.GetTime();
                double timeScale = settings.Volatile.Size;
                double renderCutoff = currentTime + timeScale;

                int keyboardHeight = height / 4;
                int noteAreaHeight = height - keyboardHeight;
                
                LastRenderedNoteCount = 0;
                LastPolyphony = 0;

                // Render notes for each key
                for (int noteNum = GetFirstNote(); noteNum < GetLastNote(); noteNum++)
                {
                    RenderNotesForKey(noteNum, currentTime, timeScale, renderCutoff, noteAreaHeight);
                }
            }
        }

        private unsafe void RenderNotesForKey(int noteNum, double currentTime, double timeScale, double renderCutoff, int noteAreaHeight)
        {
            var midiFile = file as MIDIMemoryFile;
            if (midiFile.Notes[noteNum] == null || midiFile.Notes[noteNum].Length == 0)
                return;

            // Calculate key position
            double keyLeft, keyWidth;
            GetKeyPosition(noteNum, out keyLeft, out keyWidth);
            
            int pixelLeft = (int)(keyLeft * width);
            int pixelWidth = Math.Max(1, (int)(keyWidth * width));

            uint* pixels = (uint*)bitmap.BackBuffer;
            
            // Render each note for this key
            foreach (var note in midiFile.Notes[noteNum])
            {
                if (note.end < currentTime || note.start > renderCutoff)
                    continue;

                // Calculate note position in time
                double noteStart = Math.Max(0, (note.start - currentTime) / timeScale);
                double noteEnd = Math.Min(1, (note.end - currentTime) / timeScale);
                
                if (noteEnd <= 0 || noteStart >= 1)
                    continue;

                // Convert to pixel coordinates (notes fall from top)
                int pixelTop = (int)(noteStart * noteAreaHeight);
                int pixelBottom = (int)(noteEnd * noteAreaHeight);
                int pixelHeight = Math.Max(1, pixelBottom - pixelTop);

                // Get note color
                uint noteColor = GetNoteColor(note);
                
                // Draw the note
                FillRectangle(pixels, pixelLeft, pixelTop, pixelWidth, pixelHeight, noteColor);
                
                LastRenderedNoteCount++;
                
                if (note.start <= currentTime && note.end > currentTime)
                    LastPolyphony++;
            }
        }

        private uint GetNoteColor(Note note)
        {
            // Simple color based on note number for now
            var midiFile = file as MIDIMemoryFile;
            if (midiFile.MidiNoteColors != null && note.colorPointer < midiFile.MidiNoteColors.Length)
            {
                var color = midiFile.MidiNoteColors[note.colorPointer];
                return color.rgba;
            }
            
            // Fallback color
            return 0xFF00FF00; // Green
        }

        private unsafe void FillRectangle(uint* pixels, int x, int y, int width, int height, uint color)
        {
            if (x < 0 || y < 0 || x >= this.width || y >= this.height)
                return;
                
            int maxWidth = Math.Min(width, this.width - x);
            int maxHeight = Math.Min(height, this.height - y);
            
            for (int row = 0; row < maxHeight; row++)
            {
                uint* rowStart = pixels + (y + row) * this.width + x;
                for (int col = 0; col < maxWidth; col++)
                {
                    rowStart[col] = color;
                }
            }
        }

        private unsafe void DrawRectangleBorder(uint* pixels, int x, int y, int width, int height, uint color)
        {
            // Top and bottom borders
            for (int col = 0; col < width; col++)
            {
                if (x + col >= 0 && x + col < this.width)
                {
                    if (y >= 0 && y < this.height)
                        pixels[y * this.width + x + col] = color;
                    if (y + height - 1 >= 0 && y + height - 1 < this.height)
                        pixels[(y + height - 1) * this.width + x + col] = color;
                }
            }
            
            // Left and right borders
            for (int row = 0; row < height; row++)
            {
                if (y + row >= 0 && y + row < this.height)
                {
                    if (x >= 0 && x < this.width)
                        pixels[(y + row) * this.width + x] = color;
                    if (x + width - 1 >= 0 && x + width - 1 < this.width)
                        pixels[(y + row) * this.width + x + width - 1] = color;
                }
            }
        }

        private void GetKeyPosition(int noteNum, out double left, out double width)
        {
            int whiteKeyCount = GetWhiteKeyCount();
            double whiteKeyWidth = 1.0 / whiteKeyCount;
            
            if (IsBlackKey(noteNum))
            {
                // Black key positioning
                int whiteKeysBefore = GetWhiteKeysBefore(noteNum);
                left = (whiteKeysBefore + 0.5) * whiteKeyWidth - (whiteKeyWidth * 0.3);
                width = whiteKeyWidth * 0.6;
            }
            else
            {
                // White key positioning
                int whiteKeyIndex = GetWhiteKeyIndex(noteNum);
                left = whiteKeyIndex * whiteKeyWidth;
                width = whiteKeyWidth;
            }
        }

        private bool IsBlackKey(int noteNum)
        {
            return blackKeys[noteNum % 12];
        }

        private int GetWhiteKeyCount()
        {
            int count = 0;
            for (int note = GetFirstNote(); note < GetLastNote(); note++)
            {
                if (!IsBlackKey(note))
                    count++;
            }
            return Math.Max(1, count);
        }

        private int GetWhiteKeysBefore(int noteNum)
        {
            int count = 0;
            for (int note = GetFirstNote(); note < noteNum; note++)
            {
                if (!IsBlackKey(note))
                    count++;
            }
            return count;
        }

        private int GetWhiteKeyIndex(int noteNum)
        {
            return GetWhiteKeysBefore(noteNum);
        }

        private int GetFirstNote()
        {
            switch (settings.General.KeyRange)
            {
                case KeyRangeTypes.Key88: return 21;
                case KeyRangeTypes.Key256: return 0;
                case KeyRangeTypes.KeyCustom: return settings.General.CustomFirstKey;
                default: return 0;
            }
        }

        private int GetLastNote()
        {
            switch (settings.General.KeyRange)
            {
                case KeyRangeTypes.Key88: return 109;
                case KeyRangeTypes.Key256: return 256;
                case KeyRangeTypes.KeyCustom: return settings.General.CustomLastKey;
                default: return 128;
            }
        }

        public void Dispose()
        {
            // Nothing to dispose for CPU renderer
        }
    }
}
